<template>
  <div class="flex flex-col h-full bg-white border-l border-gray-200">
    <div class="p-4 border-b border-gray-200">
      <h2 class="text-lg font-semibold text-gray-800">Main Prompt</h2>
    </div>
    <div class="flex-1 p-4">
      <textarea
        v-model="store.mainPrompt"
        class="w-full h-full p-2 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-indigo-500"
        placeholder="Your generated prompt will appear here..."
      ></textarea>
    </div>
    <div class="p-4 border-t border-gray-200">
      <button
        @click="copyToClipboard"
        class="px-4 py-2 bg-indigo-600 text-white font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        Copy to Clipboard
      </button>
    </div>
  </div>
</template>

<script setup>
import { useAppStore } from '../store';

const store = useAppStore();

const copyToClipboard = () => {
  navigator.clipboard.writeText(store.mainPrompt).then(() => {
    alert('Prompt copied to clipboard!');
  }).catch(err => {
    console.error('Failed to copy text: ', err);
    alert('Failed to copy prompt.');
  });
};
</script>

<style scoped>
/* Scoped styles for MainPanel */
</style>
